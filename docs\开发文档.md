# 小红书文案生成系统 - 开发文档

## 项目概述
小红书文案生成系统是一个基于Flask的Web应用，用于批量生成、审核和管理小红书文案内容。系统支持多客户管理、模板化生成、多级审核流程和发布管理。

## 当前开发状态
✅ **已完成功能**
- 用户认证和权限管理
- 客户管理
- 模板管理和分类
- 文案生成（简化版界面）
- 初审文案功能
- 数据库结构调整

🚧 **当前开发阶段**
- 初审文案功能已完成，正在进入下一阶段开发

## 🎯 开发基准说明
**重要：必须使用新后台系统**
- ✅ **新后台地址**：`http://127.0.0.1:5000/simple/dashboard`
- ✅ **开发基准**：基于简化版界面（`base_simple.html`）进行开发
- ❌ **禁止使用**：不要使用老后台系统进行开发
- 🔧 **菜单修改**：所有菜单调整都基于新后台的菜单结构

## 系统架构

### 技术栈
- **后端**: Flask + SQLAlchemy + MySQL
- **前端**: Bootstrap 5 + JavaScript
- **认证**: Flask-Login
- **表单**: Flask-WTF
- **数据库**: MySQL 8.0

### 系统架构说明

#### 新后台系统（开发基准）
- **访问地址**：`http://127.0.0.1:5000/simple/dashboard`
- **主模板**：`app/templates/base_simple.html`
- **路由文件**：`app/views/main_simple.py`
- **特点**：简化界面，专为新功能开发设计

#### 核心模块
1. **用户管理模块** - 用户认证、角色权限
2. **客户管理模块** - 客户信息、审核设置
3. **模板管理模块** - 文案模板、分类管理
4. **文案生成模块** - 批量生成、标记替换
5. **审核流程模块** - 多级审核工作流
6. **发布管理模块** - 内容发布和状态管理

## 菜单结构调整

### 需要删除的菜单
以下菜单需要从系统中完全移除：
- 话题管理
- 任务管理  
- 发布管理（旧版）
- 文案补充
- 文案展示
- 通知中心
- 数据统计
- 导入导出

### 新增菜单结构
```
主菜单
├── 文案生成 (已完成)
├── 初审文案 (已完成)
├── 图片上传 (待开发)
├── 最终审核 (待开发)
├── 客户审核 (待开发)
├── 发布管理 (待开发)
├── 客户管理 (已存在，需增强)
└── 系统设置 (已存在，需完善)
```

## 工作流程设计

### 文案生命周期
```
文案生成 → 初审文案 → 图片上传 → 最终审核 → 客户审核 → 发布管理
```

### 状态流转
1. **draft** - 草稿状态（生成后）
2. **pending_review** - 待初审
3. **first_approved** - 初审通过
4. **pending_image** - 待上传图片
5. **image_uploaded** - 图片已上传
6. **pending_final_review** - 待最终审核
7. **final_approved** - 最终审核通过
8. **pending_client_review** - 待客户审核
9. **client_approved** - 客户审核通过
10. **client_rejected** - 客户驳回
11. **ready_to_publish** - 准备发布
12. **published** - 已发布

## 待开发功能详细说明

### 1. 图片上传模块
**功能描述**: 为初审通过的文案上传配图

**主要功能**:
- 显示初审通过的文案列表（状态：first_approved）
- 支持多图片上传（拖拽上传、点击上传）
- 图片预览和管理
- 图片格式验证（JPG、PNG、GIF等）
- 图片大小限制设置
- 上传完成后状态更新为 image_uploaded

**技术要点**:
- 使用Flask-Upload处理文件上传
- 图片存储路径管理
- 缩略图生成
- 前端拖拽上传组件

### 2. 最终审核模块
**功能描述**: 对已上传图片的文案进行最终审核

**主要功能**:
- 显示已上传图片的文案列表（状态：image_uploaded）
- 文案内容和图片预览
- 审核操作：通过/驳回
- 驳回原因记录
- 批量审核功能
- 审核通过后状态更新

**状态流转**:
- 通过 → pending_client_review（如客户需要审核）
- 通过 → ready_to_publish（如客户无需审核）
- 驳回 → pending_review（回到初审）

### 3. 客户审核模块
**功能描述**: 客户专用审核界面和管理端监控

**客户端功能**:
- 无需登录的专用审核页面
- 通过分享链接+密钥访问
- 按任务名称、批次名称筛选
- 文案内容和图片查看
- 审核操作：通过/驳回/编辑
- 驳回理由填写

**管理端功能**:
- 客户审核进度监控
- 审核统计数据
- 客户审核历史记录
- 分享链接管理

**技术要点**:
- 生成唯一的分享链接和密钥
- 无登录状态的权限验证
- 客户专用界面设计
- 审核状态实时更新

### 4. 发布管理模块
**功能描述**: 管理客户审核通过的文案发布

**主要功能**:
- 显示待发布文案列表（状态：client_approved）
- 发布状态管理
- 发布时间设置
- 发布平台选择
- 发布结果跟踪
- 发布历史记录

**发布状态**:
- ready_to_publish - 准备发布
- publishing - 发布中
- published - 发布成功
- publish_failed - 发布失败

## 系统设置增强

### 审核流程控制
在系统设置中添加以下开关：

1. **是否需要初审**
   - 开启：生成文案后进入初审流程
   - 关闭：生成文案后直接进入图片上传

2. **是否需要最终审核**
   - 开启：图片上传后进入最终审核
   - 关闭：图片上传后直接进入客户审核

3. **客户审核设置**（在客户管理中设置）
   - 需要审核：最终审核通过后进入客户审核
   - 无需审核：最终审核通过后直接进入发布管理
   - 自动审核标记：区分自动通过和客户审核通过

### 配置项说明
```python
# 系统配置
ENABLE_FIRST_REVIEW = True/False      # 是否启用初审
ENABLE_FINAL_REVIEW = True/False      # 是否启用最终审核

# 客户配置（在客户表中）
client.need_review = True/False       # 客户是否需要审核
client.auto_approved = True/False     # 是否自动审核通过
```

## 数据库结构设计

### 核心表结构说明

#### 1. 用户和权限管理
- **users** - 用户表（管理员账户）
- **roles** - 角色表（超级管理员、普通管理员等）
- **permissions** - 权限表（功能权限定义）
- **user_roles** - 用户角色关联表
- **role_permissions** - 角色权限关联表

#### 2. 客户管理
- **clients** - 客户表
  ```sql
  主要字段：
  - id: 客户ID
  - name: 客户名称
  - need_review: 是否需要客户审核
  - auto_approved: 是否自动审核通过（新增）
  - daily_content_count: 每日文案数量
  - status: 客户状态
  ```

#### 3. 模板管理
- **template_categories** - 模板分类表
- **templates** - 模板表
  ```sql
  主要字段：
  - id: 模板ID
  - category_id: 分类ID
  - title: 模板标题
  - content: 模板内容
  - marks: 标记信息（JSON格式）
  ```

#### 4. 任务和批次管理
- **tasks** - 任务表
- **batches** - 批次表

#### 5. 文案内容管理
- **contents** - 文案内容表
  ```sql
  主要字段：
  - id: 文案ID
  - client_id: 客户ID
  - task_id: 任务ID
  - template_id: 模板ID
  - title: 文案标题
  - content: 文案内容
  - workflow_status: 工作流状态
  - client_review_status: 客户审核状态
  - internal_review_status: 内部审核状态
  - publish_priority: 发布优先级
  ```

#### 6. 图片管理（新增）
- **content_images** - 文案图片表
  ```sql
  主要字段：
  - id: 图片ID
  - content_id: 文案ID
  - image_path: 图片路径
  - thumbnail_path: 缩略图路径
  - original_name: 原始文件名
  - file_size: 文件大小
  - image_order: 图片排序
  ```

#### 7. 客户分享管理（新增）
- **client_share_links** - 客户分享链接表
  ```sql
  主要字段：
  - id: 链接ID
  - client_id: 客户ID
  - share_key: 分享密钥
  - expires_at: 过期时间
  - is_active: 是否激活
  ```

#### 8. 系统配置
- **system_settings** - 系统设置表
  ```sql
  主要字段：
  - id: 设置ID
  - key: 设置键名
  - value: 设置值
  - description: 设置描述
  ```

### 工作流状态定义
```sql
-- contents.workflow_status 可能的值：
'draft'                    -- 草稿状态
'pending_review'           -- 待初审
'first_approved'           -- 初审通过
'pending_image'            -- 待上传图片
'image_uploaded'           -- 图片已上传
'pending_final_review'     -- 待最终审核
'final_approved'           -- 最终审核通过
'pending_client_review'    -- 待客户审核
'client_approved'          -- 客户审核通过
'client_rejected'          -- 客户驳回
'ready_to_publish'         -- 准备发布
'published'                -- 已发布
```

### 数据库补充说明
现有数据库结构基本完整，只需执行 `数据库补充.sql` 脚本添加：
1. `clients.auto_approved` 字段
2. `client_share_links` 表
3. `content_images` 表
4. 系统设置默认值
5. 性能优化索引

## 开发优先级

### 第一阶段（高优先级）
1. 删除旧菜单，创建新菜单结构
2. 图片上传模块开发
3. 最终审核模块开发
4. 系统设置增强

### 第二阶段（中优先级）
1. 客户审核模块（管理端）
2. 客户专用审核页面
3. 分享链接生成和管理
4. 发布管理基础功能

### 第三阶段（低优先级）
1. 发布管理高级功能
2. 审核流程优化
3. 性能优化
4. 用户体验改进

## 技术注意事项

### 安全考虑
- 客户分享链接的安全性验证
- 文件上传安全检查
- 权限控制严格化
- 数据传输加密

### 性能优化
- 图片上传进度显示
- 大文件分片上传
- 数据库查询优化
- 缓存策略实施

### 用户体验
- 响应式设计适配
- 操作反馈及时性
- 错误提示友好化
- 界面一致性保持

## 部署和维护

### 环境要求
- Python 3.8+
- MySQL 8.0+
- Redis（缓存）
- Nginx（生产环境）

### 配置文件
- config.py - 应用配置
- requirements.txt - 依赖包
- .env - 环境变量

## 实施计划

### 立即执行任务
1. **菜单清理**（1天）
   - 删除指定的8个旧菜单
   - 更新导航模板
   - 清理相关路由和视图

2. **新菜单创建**（1天）
   - 创建4个新菜单的基础框架
   - 设置路由和权限
   - 创建基础模板

### 开发时间估算
- **图片上传模块**: 3-5天
- **最终审核模块**: 2-3天
- **客户审核模块**: 5-7天
- **发布管理模块**: 3-4天
- **系统设置增强**: 2天
- **测试和优化**: 3-5天

**总计**: 约18-26个工作日

## 关键技术实现

### 客户分享链接生成
```python
import secrets
import hashlib
from datetime import datetime, timedelta

def generate_share_link(client_id):
    # 生成随机密钥
    random_key = secrets.token_urlsafe(32)

    # 组合客户ID和时间戳
    timestamp = str(int(datetime.now().timestamp()))
    raw_data = f"{client_id}:{timestamp}:{random_key}"

    # 生成最终密钥
    share_key = hashlib.sha256(raw_data.encode()).hexdigest()[:32]

    # 设置过期时间（可配置）
    expires_at = datetime.now() + timedelta(days=30)

    return share_key, expires_at
```

### 工作流状态管理
```python
class WorkflowManager:
    def __init__(self, content):
        self.content = content

    def next_status(self):
        """根据系统设置和客户配置确定下一个状态"""
        current = self.content.workflow_status

        if current == 'draft':
            if SystemSettings.get('ENABLE_FIRST_REVIEW'):
                return 'pending_review'
            else:
                return 'pending_image'

        elif current == 'first_approved':
            return 'pending_image'

        elif current == 'image_uploaded':
            if SystemSettings.get('ENABLE_FINAL_REVIEW'):
                return 'pending_final_review'
            else:
                return self._after_final_review()

        elif current == 'final_approved':
            return self._after_final_review()

    def _after_final_review(self):
        """最终审核后的状态判断"""
        client = self.content.client
        if client.need_review:
            return 'pending_client_review'
        else:
            # 标记为自动审核通过
            self.content.auto_approved = True
            return 'ready_to_publish'
```

### 图片上传处理
```python
from werkzeug.utils import secure_filename
import os
from PIL import Image

class ImageUploadHandler:
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

    def __init__(self, upload_folder):
        self.upload_folder = upload_folder

    def save_image(self, file, content_id):
        """保存图片并生成缩略图"""
        if not self._allowed_file(file.filename):
            raise ValueError("不支持的文件格式")

        filename = secure_filename(file.filename)
        timestamp = str(int(time.time()))
        filename = f"{content_id}_{timestamp}_{filename}"

        filepath = os.path.join(self.upload_folder, filename)
        file.save(filepath)

        # 生成缩略图
        self._generate_thumbnail(filepath)

        return filename

    def _generate_thumbnail(self, filepath):
        """生成缩略图"""
        with Image.open(filepath) as img:
            img.thumbnail((300, 300))
            thumb_path = filepath.replace('.', '_thumb.')
            img.save(thumb_path)
```

## API接口设计

### 客户审核API
```
GET  /client-review/{share_key}           # 客户审核页面
POST /client-review/{share_key}/approve   # 客户审核通过
POST /client-review/{share_key}/reject    # 客户审核驳回
POST /client-review/{share_key}/edit      # 客户编辑文案
GET  /client-review/{share_key}/contents  # 获取文案列表
```

### 图片上传API
```
POST /api/upload/image                    # 上传图片
GET  /api/images/{content_id}            # 获取文案图片
DELETE /api/images/{image_id}            # 删除图片
```

### 系统设置API
```
GET  /api/settings                       # 获取系统设置
POST /api/settings                       # 更新系统设置
```

## 测试策略

### 单元测试
- 工作流状态转换测试
- 图片上传功能测试
- 权限验证测试
- 分享链接生成测试

### 集成测试
- 完整审核流程测试
- 客户审核流程测试
- 系统设置影响测试

### 用户验收测试
- 管理员操作流程
- 客户审核体验
- 移动端适配测试

---

**文档版本**: v2.0
**最后更新**: 2025-07-21
**维护人员**: 开发团队
**下次更新**: 根据开发进度及时更新
