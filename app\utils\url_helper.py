#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
URL辅助工具
"""

from flask import request, current_app
from urllib.parse import urlparse


def get_external_base_url():
    """
    获取外部访问的基础URL

    优先级：
    1. 代理头信息（X-Forwarded-Proto, X-Forwarded-Host）
    2. Host头信息（适用于反向代理环境）
    3. 配置的外部域名（EXTERNAL_DOMAIN）
    4. Flask检测的域名（request.scheme, request.host）

    Returns:
        str: 外部访问的基础URL，如 https://xhs2.ke999.cn
    """
    try:
        # 1. 优先使用代理头信息
        forwarded_proto = request.headers.get('X-Forwarded-Proto')
        forwarded_host = request.headers.get('X-Forwarded-Host')

        if forwarded_proto and forwarded_host:
            return f"{forwarded_proto}://{forwarded_host}"

        # 2. 检查Host头信息（适用于反向代理环境）
        host_header = request.headers.get('Host')
        if host_header and not host_header.startswith(('127.0.0.1', 'localhost')):
            # 如果Host头包含端口号，需要处理
            if ':' in host_header and not host_header.endswith(':443') and not host_header.endswith(':80'):
                # 非标准端口，保留端口号
                protocol = 'https' if request.is_secure else 'http'
                return f"{protocol}://{host_header}"
            else:
                # 标准端口或包含标准端口号，使用https
                host_without_port = host_header.split(':')[0]
                return f"https://{host_without_port}"

        # 3. 使用配置的外部域名
        external_domain = current_app.config.get('EXTERNAL_DOMAIN')
        if external_domain:
            # 如果配置的域名已经包含协议，直接使用
            if external_domain.startswith(('http://', 'https://')):
                parsed = urlparse(external_domain)
                return f"{parsed.scheme}://{parsed.netloc}"
            else:
                # 如果没有协议，默认使用https
                return f"https://{external_domain}"

        # 4. 回退到Flask检测的域名
        flask_url = f"{request.scheme}://{request.host}"

        # 如果是本地开发环境，直接使用Flask检测的域名
        if request.host.startswith(('127.0.0.1', 'localhost')):
            return flask_url

        # 如果不是本地环境但没有配置外部域名，使用默认的生产域名
        return "https://xhs2.ke999.cn"

    except Exception as e:
        current_app.logger.warning(f"获取外部域名失败: {e}")
        # 最后的回退方案
        return "https://xhs2.ke999.cn"


def build_share_url(share_key, access_key=None, task_id=None):
    """
    构建分享链接URL
    
    Args:
        share_key: 分享密钥
        access_key: 访问密钥（可选）
        task_id: 任务ID（可选）
    
    Returns:
        str: 完整的分享链接URL
    """
    try:
        base_url = get_external_base_url()
        share_url = f"{base_url}/client-review/{share_key}"
        
        # 添加参数
        params = []
        if access_key:
            params.append(f"key={access_key}")
        if task_id:
            params.append(f"task={task_id}")
        
        if params:
            share_url += "?" + "&".join(params)
        
        return share_url
        
    except Exception as e:
        current_app.logger.error(f"构建分享链接失败: {e}")
        # 回退方案
        return f"/client-review/{share_key}"


def build_image_url(image_path):
    """
    构建图片的完整URL

    Args:
        image_path: 图片相对路径，如 'images/202508/content_256_1754152220_116edd29.webp'

    Returns:
        str: 完整的图片URL，如 'https://xhs2.ke999.cn/static/uploads/images/202508/content_256_1754152220_116edd29.webp'
    """
    try:
        # 如果已经是完整URL，直接返回
        if image_path.startswith(('http://', 'https://')):
            return image_path

        # 获取基础URL
        base_url = get_external_base_url()

        # 调试信息：记录URL生成过程
        current_app.logger.debug(f"构建图片URL - 输入路径: {image_path}, 基础URL: {base_url}")

        # 处理路径
        if image_path.startswith('/static/uploads/'):
            # 已经包含完整的静态路径前缀，直接使用
            full_url = f"{base_url}{image_path}"
        elif image_path.startswith('uploads/'):
            # 已经包含uploads前缀，添加static前缀
            full_url = f"{base_url}/static/{image_path}"
        else:
            # 相对路径，添加完整前缀
            if image_path.startswith('/'):
                image_path = image_path[1:]  # 移除开头的斜杠
            full_url = f"{base_url}/static/uploads/{image_path}"

        current_app.logger.debug(f"构建图片URL - 最终URL: {full_url}")
        return full_url

    except Exception as e:
        current_app.logger.error(f"构建图片URL失败: {e}")
        # 回退方案：返回相对路径
        if image_path.startswith('/static/'):
            return image_path
        elif image_path.startswith('uploads/'):
            return f"/static/{image_path}"
        else:
            return f"/static/uploads/{image_path}"


def log_request_info():
    """
    记录请求信息，用于调试
    """
    try:
        current_app.logger.info(f"请求信息调试:")
        current_app.logger.info(f"  - request.scheme: {request.scheme}")
        current_app.logger.info(f"  - request.host: {request.host}")
        current_app.logger.info(f"  - request.url: {request.url}")
        current_app.logger.info(f"  - X-Forwarded-Proto: {request.headers.get('X-Forwarded-Proto')}")
        current_app.logger.info(f"  - X-Forwarded-Host: {request.headers.get('X-Forwarded-Host')}")
        current_app.logger.info(f"  - Host: {request.headers.get('Host')}")
        current_app.logger.info(f"  - 配置的外部域名: {current_app.config.get('EXTERNAL_DOMAIN')}")
        current_app.logger.info(f"  - 最终基础URL: {get_external_base_url()}")
    except Exception as e:
        current_app.logger.error(f"记录请求信息失败: {e}")
