# 小红书文案生成系统需求文档

## 1. 系统概述

### 1.1 系统目标
开发一套小红书文案生成系统，通过模板和关键词替换的方式批量生成小红书文案，并提供审核、编辑和发布功能。

### 1.2 系统架构
- 采用Python开发
- 单体架构（不采用前后端分离）
- Web框架：Flask + Flask-Admin（提供现成管理界面）
- 数据库：MySQL 5.7（直接连接）
- 数据库连接：PyMySQL
- 权限管理：Flask-Login（简单易用的用户认证系统）
- 前端：Bootstrap + jQuery

### 1.3 系统流程概述
1. **模板管理**：创建分类和模板，定义标记
2. **客户管理**：添加客户信息，设置展示规则
3. **任务管理**：为客户创建任务，可分批次生成文案
4. **文案生成**：选择模板和关键词，批量生成文案
5. **文案审核流程**：
   - 初审（可在系统设置中关闭）
   - 图片上传
   - 最终审核（可在系统设置中关闭）
   - 客户审核
6. **文案发布**：设置发布优先级，提供API接口

## 2. 功能需求

### 2.1 用户角色管理

#### 2.1.1 角色类型
- **超级管理员**：系统全部权限，包括系统设置
- **普通管理员**：基础管理功能
- **文案审核员**：初审文案内容
- **图文编辑**：图片上传与排版
- **模板管理员**：模板创建与管理
- **发布管理员**：管理文案发布优先级

#### 2.1.2 权限设置
- 支持自定义角色权限配置
- 可为每个用户分配一个或多个角色
- 系统管理员和高级管理员可配置系统设置

### 2.2 模板管理

#### 2.2.1 模板分类
- 支持创建多级模板分类（如服饰类、美食类等）
- 分类支持增删改查操作

#### 2.2.2 模板创建
- 支持在模板中添加标记用于内容替换
- 标记格式为特定符号包围的关键词，如`{标题}`、`{内容}`等
- 相同标记替换为相同内容（如多处使用`{品牌}`将替换为相同的品牌名称）
- 支持富文本编辑器，可设置字体、颜色、大小等格式
- 支持模板预览功能，可实时查看标记替换效果
- 支持模板复制功能，便于批量创建相似模板

#### 2.2.3 标记系统
- 标记定义：使用花括号`{}`包围的关键词作为标记，如`{标题}`
- 标记分类：普通标记（替换为文本）和特殊标记（如话题标记）
- 标记重复使用：同一模板中相同标记会替换为相同内容
- 标记管理：系统提供标记库，可预定义常用标记
- 标记验证：创建模板时自动检测未定义标记
- 标记替换规则：
  - 普通标记：直接替换为对应文本
  - 话题标记：替换为系统从话题库中选取的话题列表
  - 位置标记：替换为预设的位置信息
  - @用户标记：替换为预设的@用户列表，用于小红书文案中@其他用户功能
- 支持嵌套标记，如`{品牌的{颜色}产品}`

### 2.3 话题管理

#### 2.3.1 话题分类
- 必选话题：必须包含在文案中
- 随机话题：从话题池中随机选取

#### 2.3.2 话题设置
- 支持设置必选话题数量
- 支持设置随机话题数量
- 系统自动计算总话题数（必选+随机）

### 2.4 客户管理

#### 2.4.1 客户信息
- 基本信息：名称、联系方式等
- 审核设置：是否需要客户审核
- 文案设置：每日展示数量、展示时间等

#### 2.4.2 客户权限
- **查看文案**：允许查看文案内容（默认开启）
- **编辑文案**：允许修改文案内容（默认开启）
- **通过/拒绝文案**：允许审核文案（默认开启）
- **查看历史记录**：允许查看文案修改历史（默认开启）
- **文案排序**：允许调整文案展示顺序（可选）
- **文案筛选**：允许按状态、日期等条件筛选文案（默认开启）
- 权限控制粒度：可单独设置每项权限的开启/关闭
- 权限模板：提供常用权限组合（如"仅查看"、"完全权限"等）
- 权限有效期：可设置权限的有效时间段
- 默认权限：创建客户时默认拥有查看权限

#### 2.4.3 客户分享
- 生成分享链接
- 设置访问密码（4位数字或字母数字组合）
- 设置链接有效期（默认为永久有效）
- 有效期选项：永久、1天、3天、7天、30天、自定义天数
- 设置客户权限（查看/编辑/通过拒绝）
- 权限使用记录：记录客户权限使用情况，便于审计

### 2.5 任务管理

#### 2.5.1 任务创建
- 为客户创建文案生成任务
- 任务名称：默认为当前日期，支持自定义
- 任务描述：可选填写
- 任务状态：进行中/已完成
- 支持为同一客户创建多个任务（如5月任务、6月任务）

#### 2.5.2 任务批次
- 支持在一个任务内分多批次生成文案
- 批次管理：可查看每个批次的文案数量和状态
- 支持为一个任务生成超出需求数量的文案（如需要30篇，可生成50篇）
- 支持在任务中追加新批次的文案

### 2.6 文案生成

#### 2.6.1 基础设置
- 选择客户
- 选择任务（新建或选择已有任务）
- 选择模板分类
- 设置生成数量
- 设置每日展示数量
- 设置展示开始时间
- 设置文案间隔时间范围

#### 2.6.2 关键词设置
- 为每个标记提供替换内容
- 支持批量导入关键词
- 支持关键词随机化（从预设列表中随机选择）
- 支持关键词组合（多个关键词按规则组合）

#### 2.6.3 生成规则
- 支持从模板池中随机抽取不重复模板
- 支持指定模板生成
- 支持批量生成多篇文案
- 支持检测重复文案，避免内容过于相似

### 2.7 文案审核流程

#### 2.7.1 内部审核
- 文案审核员初审
- 图文编辑上传图片并排版
- 管理员最终审核
- 审核自动化配置：
  - 文案审核自动通过开关（系统设置中配置）
  - 管理员最终审核自动通过开关（系统设置中配置）
  - 一键通过所有待审核文案功能
  - 自动通过延迟时间设置（可设置多久后自动通过）
- 审核状态跟踪：记录每个审核环节的状态、时间和操作人
- 文案锁定机制：当一个用户正在编辑时，其他用户只能查看不能编辑
- 工作流程控制：一个环节完成后才能进入下一环节
- 冲突提醒：当检测到潜在冲突时，系统自动提醒相关用户

#### 2.7.2 客户审核
- 客户查看文案
- 客户编辑文案（如有权限）
- 客户通过/拒绝文案
- 拒绝理由：
  - 支持选择预设快捷理由
  - 支持自定义理由输入
  - 可配置理由是否必填
- 拒绝后处理：
  - 文案状态回退到初审状态
  - 如初审已关闭，则自动通过初审进入图片上传阶段
  - 显示所有拒绝历史记录及理由（类似聊天框形式）
  - 自动发送通知给相关处理人员（根据当前审核流程配置）
  - 可配置拒绝通知的处理优先级（默认为高优先级）

#### 2.7.3 审核配置
- 支持配置是否需要文案审核环节
- 支持配置是否需要客户审核
- 支持自动确认时间设置
- 支持审核流程自定义（可选择跳过某些环节）
- 支持不同客户设置不同的审核流程
- 审核流程联动机制：当系统设置更改审核流程时，相应调整客户权限设置
- 客户审核始终作为独立环节：无论系统如何设置内部审核流程，客户审核环节保持独立

### 2.8 发布管理

#### 2.8.1 发布状态
- 待发布：客户审核通过后的状态
- 发布中：已被API获取但未收到状态更新的状态
- 已发布：已通过API调用发布的状态
- 发布失败：API调用失败的状态
- 发布超时：发布中状态超过设定时间未收到状态更新

#### 2.8.2 发布优先级
- 高优先级：优先发布
- 中优先级：默认优先级
- 低优先级：最后发布
- 支持批量设置和调整优先级

#### 2.8.3 发布设置
- 支持设置发布时间
- 支持设置发布平台
- 支持设置发布账号
- 支持发布后状态追踪

#### 2.8.4 发布超时管理
- 设置发布超时时间（可在系统设置中配置）
- 超时处理策略配置
- 超时文案查看和处理界面
- 超时统计和报警功能

#### 2.8.5 批量状态管理
- 批量状态转换界面
- 状态筛选和批量选择功能
- 批量操作日志记录
- 操作确认和信息填写弹窗

### 2.9 文案补充机制

#### 2.9.1 补充选项
- 立即补充：马上向客户展示新文案
- 延时补充：设置延迟时间后展示
- 不补充：不替换被删除的文案

#### 2.9.2 补充触发
- 文案被删除时提示补充
- 支持批量补充多篇文案
- 提供补充文案专用入口
- 删除文案后弹出补充选项窗口
- 支持设置补充文案的来源（未分配文案池/新生成文案）

### 2.10 文案展示

#### 2.10.1 展示规则
- 仅展示当天及之前日期的文案
- 按照设定的时间点和间隔展示
- 文案必须经过审核通过才能展示
- 支持设置文案展示顺序（固定/随机）

#### 2.10.2 展示设置
- 每日展示数量
- 展示开始时间
- 文案间隔时间（固定值或范围值）
- 支持设置展示时间范围（如9:00-18:00）
- 支持设置特定日期不展示文案

### 2.11 通知机制

#### 2.11.1 通知事件
- 文案审核状态变更通知
- 客户编辑/拒绝通知
  - 客户拒绝文案时，根据审核流程设置自动通知相关人员：
    - 如初审环节开启：通知文案审核员
    - 如初审环节关闭：通知图文编辑
    - 同时通知该客户的管理员
  - 通知包含拒绝理由和文案链接，便于快速处理
- 文案发布通知
- 系统设置变更通知（仅发送给管理员）
- 每个通知包含：
  - 通知标题
  - 通知内容
  - 发生时间
  - 相关文案链接（可直接跳转到对应文案）
  - 操作人信息
  - 处理优先级标识

#### 2.11.2 通知设置
- 通知开关（全局和单个事件类型）
- 通知接收角色设置（可按角色类型筛选通知接收者）
- 站内信通知
- 通知中心：集中展示所有通知
- 通知标记：已读/未读状态
- 通知筛选：按类型、时间、状态筛选
- 通知优先级：高/中/低，影响显示顺序和提醒方式

### 2.12 API接口

#### 2.12.1 文案获取接口
- 获取已审核通过的文案（状态为"待发布"）
- 单次调用获取一篇文案
- 获取逻辑：
  - 按发布优先级排序（高 > 中 > 低）
  - 同优先级按创建时间排序（先创建先获取）
  - 获取成功后自动将文案状态标记为"发布中"
- 无可用文案时返回明确提示信息
- 支持按日期筛选
- 支持按客户筛选
- 支持分页和数量限制
- 支持按状态筛选（已发布/未发布）
- 支持按发布优先级筛选
- 接口返回数据：
  - 文案ID
  - 文案内容
  - 标题
  - 话题列表
  - 定位信息
  - 图片URL列表
  - 审核状态
  - 创建和修改时间
  - 扩展字段（ext_json内容，支持自定义属性）
  - 获取结果状态码和消息

#### 2.12.2 发布状态更新接口
- 用于第三方软件更新文案发布状态
- 必须参数：
  - 文案ID
  - 发布状态（成功/失败）
- 可选参数：
  - 发布链接URL
  - 发布账号信息
  - 发布平台
  - 发布时间
  - 失败原因
  - 其他扩展信息（JSON格式）
- 返回数据：
  - 操作结果（成功/失败）
  - 错误信息（如有）
- 支持批量更新（可同时提交多篇文案的发布状态）
- 支持扩展字段提交（可通过ext_info字段提交任意额外信息）

#### 2.12.3 数据格式
- 文案内容
- 标题
- 话题列表
- 定位信息
- 图片链接
- 审核状态
- 创建和修改时间
- 发布优先级

### 2.13 系统设置

#### 2.13.1 基础设置
- 默认每日展示数量
- 默认展示开始时间
- 默认文案间隔范围
- 默认链接有效期（永久）
- 系统设置访问权限控制：
  - 仅超级管理员和高级管理员可访问系统设置
  - 设置修改需要二次确认
  - 关键设置修改会通知所有管理员

#### 2.13.2 高级设置
- 审核流程配置
  - 文案审核自动通过开关
  - 管理员最终审核自动通过开关
  - 自动通过延迟时间设置
  - 审核流程可视化界面，清晰展示当前流程设置
- 自动确认时间设置
- 拒绝理由是否必填
- 快捷理由管理
- 通知设置
- 系统日志级别
- 数据归档策略配置
- 设置优先级规则：
  - 客户级设置优先于系统级设置
  - 界面明确标识当前使用的是系统默认设置还是客户自定义设置
  - 提供"重置为系统默认"选项
- 发布超时设置：
  - 发布超时时间配置（默认24小时）
  - 超时处理策略选择
  - 超时通知设置

#### 2.13.3 管理员分级
- **超级管理员**：可访问和修改所有系统设置
- **高级管理员**：可访问和修改部分系统设置（可配置）
- **普通管理员**：无系统设置权限，仅能执行日常管理操作
- **专业角色**：特定功能模块的专业操作权限（如模板管理员）

### 2.14 文案状态管理

#### 2.14.1 文案流转状态
- **待生成**：任务创建但尚未生成文案
- **待初审**：文案已生成，等待初审
- **初审通过**：文案初审已通过，等待上传图片
- **待上传图片**：等待图文编辑上传图片
- **图片已上传**：图片已上传，等待最终审核
- **待最终审核**：等待管理员最终审核
- **待客户审核**：等待客户审核
- **客户已拒绝**：客户拒绝，需要修改
- **客户已通过**：客户已通过审核，等待发布
- **待发布**：所有审核已通过，等待发布
- **已发布**：已通过API发布到平台

#### 2.14.2 发布状态管理
发布状态单独存储，与文案流转状态分开管理：
- **未发布**：尚未尝试发布
- **发布中**：已被API获取，正在发布过程中
- **发布成功**：成功发布到平台
- **发布失败**：发布尝试失败
- **部分发布**：多平台发布时部分成功部分失败
- **发布超时**：发布中状态超过设定时间未收到状态更新

#### 2.14.3 状态字段设计
数据库中使用多个字段存储不同类型的状态：
- **workflow_status**：文案工作流状态，存储文案流转状态
- **publish_status**：发布状态，存储文案发布状态
- **client_review_status**：客户审核状态（未审核/已通过/已拒绝）
- **internal_review_status**：内部审核状态（未审核/初审通过/最终审核通过）
- **publish_time**：发布时间（API获取时间）
- **status_update_time**：状态最后更新时间

#### 2.14.4 状态转换规则
- 文案生成后自动设置为"待初审"状态
- 初审通过后自动设置为"待上传图片"状态
- 图片上传后自动设置为"待最终审核"状态
- 最终审核通过后：
  - 如需客户审核：设置为"待客户审核"状态
  - 如不需客户审核：设置为"待发布"状态
- 客户拒绝后：
  - 如需初审：回退到"待初审"状态
  - 如不需初审：设置为"待上传图片"状态
- 客户通过后自动设置为"待发布"状态
- API获取文案后自动将publish_status更新为"发布中"，并记录publish_time
- 发布状态更新API调用后更新publish_status，不影响workflow_status
- 发布成功后，publish_status更新为"发布成功"，workflow_status更新为"已发布"
- 发布中状态超过设定时间未收到更新，自动标记为"发布超时"状态

#### 2.14.5 发布超时处理
- 系统设置中可配置发布超时时间（默认24小时）
- 超时处理选项：
  - **自动重置**：超时后自动将状态重置为"待发布"，可再次被API获取
  - **保持超时**：保持"发布超时"状态，等待人工处理
  - **自动失败**：超时后自动将状态设为"发布失败"
- 定时任务定期检查发布中状态的文案，处理超时情况

#### 2.14.6 批量状态管理
- 支持批量选择文案进行状态转换
- 批量操作选项：
  - **重置为待发布**：将选中文案状态重置为"待发布"
  - **标记为已发布**：将选中文案标记为"发布成功"和"已发布"
    - 可选填写发布信息（弹窗）
    - 批量操作时仅显示确认框
  - **标记为发布失败**：将选中文案标记为"发布失败"
    - 可选填写失败原因
  - **删除文案**：删除选中文案（仅对未发布或发布失败的文案可用）
- 批量操作权限控制：仅发布管理员和超级管理员可执行

## 3. 非功能需求

### 3.1 性能需求
- 支持同时在线用户：50人
- 页面响应时间：<2秒
- 文案生成速度：100篇/分钟

### 3.2 安全需求
- 用户认证与授权
- 密码加密存储
- 操作日志记录
- 敏感数据保护

### 3.3 可用性需求
- 系统可用性：99.9%
- 支持主流浏览器
- 响应式设计，支持移动设备访问

### 3.4 可扩展性需求
- 支持未来功能模块扩展
- 支持用户量增长
- 支持数据量增长

## 4. 数据库设计

### 4.1 核心数据表

#### 4.1.1 用户表（users）
- id：用户ID，主键
- username：用户名
- password：密码（加密存储）
- email：邮箱
- real_name：真实姓名
- phone：电话
- status：状态（启用/禁用）
- created_at：创建时间
- last_login：最后登录时间

#### 4.1.2 角色表（roles）
- id：角色ID，主键
- name：角色名称
- description：角色描述
- created_at：创建时间

#### 4.1.3 用户角色关联表（user_roles）
- id：主键
- user_id：用户ID，外键关联users表
- role_id：角色ID，外键关联roles表

#### 4.1.4 权限表（permissions）
- id：权限ID，主键
- name：权限名称
- code：权限代码
- description：权限描述

#### 4.1.5 角色权限关联表（role_permissions）
- id：主键
- role_id：角色ID，外键关联roles表
- permission_id：权限ID，外键关联permissions表

#### 4.1.6 模板分类表（template_categories）
- id：分类ID，主键
- name：分类名称
- parent_id：父分类ID，自关联
- sort_order：排序顺序
- created_at：创建时间
- updated_at：更新时间

#### 4.1.7 模板表（templates）
- id：模板ID，主键
- category_id：分类ID，外键关联template_categories表
- title：模板标题
- content：模板内容（包含标记）
- creator_id：创建者ID，外键关联users表
- created_at：创建时间
- updated_at：更新时间
- status：状态（启用/禁用）

#### 4.1.8 标记定义表（template_marks）
- id：标记ID，主键
- name：标记名称
- description：标记描述
- type：标记类型（普通/话题/位置/@用户等）
- created_at：创建时间

#### 4.1.9 话题表（topics）
- id：话题ID，主键
- name：话题名称
- type：话题类型（必选/随机）
- priority：优先级
- created_at：创建时间
- updated_at：更新时间

#### 4.1.10 话题关联表（topic_relations）
- id：主键
- topic_id：话题ID，外键关联topics表
- related_topic_id：关联话题ID，外键关联topics表
- weight：关联权重

#### 4.1.11 客户表（clients）
- id：客户ID，主键
- name：客户名称
- contact：联系人
- phone：联系电话
- email：联系邮箱
- need_review：是否需要客户审核（布尔值）
- daily_content_count：每日展示数量
- display_start_time：展示开始时间
- interval_min：最小间隔时间（分钟）
- interval_max：最大间隔时间（分钟）
- created_at：创建时间
- updated_at：更新时间
- status：状态（启用/禁用）
- ext_json：扩展字段（JSON格式，存储额外属性）

#### 4.1.12 客户分享链接表（client_shares）
- id：分享ID，主键
- client_id：客户ID，外键关联clients表
- access_token：访问令牌
- password：访问密码
- expires_at：过期时间（null表示永久有效）
- view_permission：查看权限（布尔值）
- edit_permission：编辑权限（布尔值）
- review_permission：审核权限（布尔值）
- created_at：创建时间
- created_by：创建者ID，外键关联users表

#### 4.1.13 任务表（tasks）
- id：任务ID，主键
- client_id：客户ID，外键关联clients表
- name：任务名称
- description：任务描述
- status：状态（进行中/已完成）
- target_count：目标文案数量
- actual_count：实际生成文案数量
- created_at：创建时间
- updated_at：更新时间
- created_by：创建者ID，外键关联users表

#### 4.1.14 批次表（batches）
- id：批次ID，主键
- task_id：任务ID，外键关联tasks表
- name：批次名称
- content_count：文案数量
- created_at：创建时间
- created_by：创建者ID，外键关联users表

#### 4.1.15 文案表（contents）
- id：文案ID，主键
- client_id：客户ID，外键关联clients表
- task_id：任务ID，外键关联tasks表
- batch_id：批次ID，外键关联batches表
- template_id：模板ID，外键关联templates表
- title：文案标题
- content：文案内容
- topics：话题列表（JSON格式）
- location：定位信息
- image_urls：图片URL列表（JSON格式）
- display_date：展示日期
- display_time：展示时间
- workflow_status：工作流状态（待初审/初审通过/待上传图片/图片已上传/待最终审核/待客户审核/客户已拒绝/客户已通过/待发布/已发布）
- publish_status：发布状态（未发布/发布中/发布成功/发布失败/部分发布/发布超时）
- client_review_status：客户审核状态（未审核/已通过/已拒绝）
- internal_review_status：内部审核状态（未审核/初审通过/最终审核通过）
- publish_priority：发布优先级（高/中/低）
- publish_time：API获取时间
- status_update_time：状态最后更新时间
- created_at：创建时间
- updated_at：更新时间
- created_by：创建者ID，外键关联users表
- reviewer_id：审核者ID，外键关联users表
- review_time：审核时间
- ext_json：扩展字段（JSON格式，存储额外属性）

#### 4.1.21 发布记录表（publish_records）
- id：记录ID，主键
- content_id：文案ID，外键关联contents表
- status：发布状态（成功/失败）
- platform：发布平台
- account：发布账号
- publish_url：发布链接
- publish_time：发布时间
- error_message：失败原因
- ext_info：扩展信息（JSON格式，存储任意额外信息）
- created_at：创建时间
- updated_at：更新时间

#### 4.1.16 文案历史表（content_history）
- id：历史记录ID，主键
- content_id：文案ID，外键关联contents表
- title：历史标题
- content：历史内容
- editor_id：编辑者ID，外键关联users表
- edit_time：编辑时间
- is_client_edit：是否客户编辑（布尔值）

#### 4.1.17 拒绝理由表（rejection_reasons）
- id：理由ID，主键
- content_id：文案ID，外键关联contents表
- reason：拒绝理由
- created_at：创建时间
- created_by：创建者ID，外键关联users表或clients表
- is_client：是否客户创建（布尔值）

#### 4.1.18 快捷理由表（quick_reasons）
- id：快捷理由ID，主键
- content：理由内容
- sort_order：排序顺序
- created_at：创建时间

#### 4.1.19 通知表（notifications）
- id：通知ID，主键
- title：通知标题
- content：通知内容
- type：通知类型（审核状态变更/客户操作/发布通知等）
- related_content_id：相关文案ID，外键关联contents表
- created_at：创建时间
- is_read：是否已读（布尔值）
- recipient_id：接收者ID，外键关联users表

#### 4.1.20 系统配置表（system_settings）
- id：配置ID，主键
- key：配置键
- value：配置值
- description：配置描述
- updated_at：更新时间
- updated_by：更新者ID，外键关联users表

### 4.2 数据库索引设计

#### 4.2.1 用户表索引
- username：唯一索引，加速用户登录查询
- email：唯一索引，支持邮箱登录和查找
- status：普通索引，加速筛选有效用户

#### 4.2.2 文案表索引
- client_id + status：组合索引，加速查询特定客户的特定状态文案
- task_id：普通索引，加速查询特定任务的文案
- display_date：普通索引，加速按日期查询文案
- publish_priority：普通索引，加速按优先级查询文案
- created_at：普通索引，支持时间范围查询

#### 4.2.3 任务表索引
- client_id：普通索引，加速查询特定客户的任务
- status：普通索引，加速查询特定状态的任务

#### 4.2.4 通知表索引
- recipient_id + is_read：组合索引，加速查询用户未读通知
- related_content_id：普通索引，加速查询特定文案相关通知

### 4.3 数据库扩展性设计

#### 4.3.1 扩展字段设计
- 核心表（用户表、客户表、文案表等）添加ext_json字段
- ext_json字段使用JSON格式存储，可动态添加新属性
- 系统支持通过配置将ext_json中的字段映射到界面表单

#### 4.3.2 分表策略
- 文案表按时间分表：contents_202301, contents_202302...
- 通知表按用户分表：notifications_user_1, notifications_user_2...
- 历史记录表按对象分表：history_contents, history_templates...

#### 4.3.3 数据归档策略
- 历史数据定期归档到归档表
- 归档数据可通过专用接口查询
- 归档策略可在系统设置中配置

## 5. 界面设计要求

### 5.1 管理后台
- 简洁清晰的管理界面
- 直观的操作流程
- 响应式布局
- 左侧固定导航栏，右侧内容区域
- 顶部显示当前用户信息和通知
- 支持深色/浅色模式切换
- 表格数据支持排序、筛选和分页
- 批量操作支持（如批量删除、批量审核）
- 数据可视化展示（如文案生成数量统计图表）

### 5.2 客户界面
- 简单易用
- 清晰展示文案内容
- 明确的操作按钮
- 响应式设计，适配移动设备
- 文案列表按日期分组展示
- 文案详情页支持图文预览
- 编辑界面简洁直观
- 审核操作流程清晰（通过/拒绝按钮醒目）
- 拒绝理由选择和输入界面友好

### 5.3 页面设计规范
- 配色方案：主色调+辅助色
- 字体：系统默认字体，正文14px，标题16-20px
- 按钮样式统一
- 表单验证反馈及时
- 操作成功/失败提示明确
- 加载状态显示
- 空数据状态显示
- 错误页面友好提示

### 5.4 核心页面列表

#### 5.4.1 管理后台页面
1. **登录页**
   - 用户名/密码登录
   - 记住密码功能
   - 验证码功能

2. **仪表盘**
   - 系统概览
   - 待处理任务提醒
   - 数据统计图表

3. **用户管理**
   - 用户列表
   - 用户添加/编辑
   - 角色分配

4. **角色权限管理**
   - 角色列表
   - 角色添加/编辑
   - 权限分配

5. **模板管理**
   - 模板分类管理
   - 模板列表
   - 模板创建/编辑
   - 模板预览

6. **话题管理**
   - 话题列表
   - 话题添加/编辑
   - 话题关联设置

7. **客户管理**
   - 客户列表
   - 客户添加/编辑
   - 客户分享链接管理

8. **文案生成**
   - 基础设置
   - 关键词设置
   - 生成预览
   - 批量生成

9. **文案审核**
   - 待审核文案列表
   - 文案审核界面
   - 图片上传功能
   - 审核历史记录

10. **文案管理**
    - 文案列表（按客户/日期筛选）
    - 文案详情
    - 文案编辑
    - 文案历史版本

11. **通知中心**
    - 通知列表
    - 通知详情
    - 通知设置

12. **系统设置**
    - 基础设置
    - 审核流程设置
    - 通知设置
    - 快捷理由管理

#### 5.4.2 客户界面页面
1. **登录页**
   - 密码登录
   - 链接有效期提示

2. **文案列表**
   - 按日期分组
   - 文案状态标识
   - 文案预览

3. **文案详情**
   - 文案内容展示
   - 图片展示
   - 话题展示
   - 操作按钮（通过/拒绝/编辑）

4. **文案编辑**
   - 文案编辑表单
   - 保存/取消按钮

5. **拒绝理由**
   - 快捷理由选择
   - 自定义理由输入
   - 提交/取消按钮

## 6. 开发与部署

### 6.1 开发环境
- Python 3.8+
- MySQL 5.7+
- 开发工具自选
- 版本控制：Git
- 开发规范：
  - PEP 8 Python代码规范
  - 代码注释规范
  - 提交信息规范

### 6.2 技术栈详细说明
- **后端框架**：Flask
- **管理界面**：Flask-Admin
- **数据库连接**：PyMySQL
- **用户认证**：Flask-Login
- **表单处理**：Flask-WTF
- **前端框架**：Bootstrap 5
- **JavaScript库**：jQuery
- **富文本编辑器**：CKEditor 5
- **图表库**：Chart.js
- **文件上传**：Flask-Uploads
- **定时任务**：APScheduler

### 6.3 开发流程
1. **环境搭建**
   - 安装Python和依赖包
   - 配置MySQL数据库
   - 设置开发环境变量

2. **数据库初始化**
   - 创建数据库
   - 初始化表结构
   - 导入基础数据

3. **核心功能开发**
   - 用户认证系统
   - 模板管理
   - 文案生成
   - 审核流程
   - 客户界面

4. **测试**
   - 单元测试
   - 集成测试
   - 用户界面测试

### 6.4 部署要求
- 支持Windows/Linux服务器部署
- 支持Docker容器化部署
- 提供详细部署文档
- 环境要求：
  - CPU: 2核+
  - 内存: 4GB+
  - 存储: 50GB+
  - 带宽: 5Mbps+

### 6.5 部署步骤
1. **服务器准备**
   - 安装操作系统
   - 配置网络
   - 安装必要软件（Python、MySQL等）

2. **应用部署**
   - 代码部署
   - 依赖安装
   - 配置文件设置

3. **数据库部署**
   - 创建数据库
   - 导入初始数据
   - 配置备份策略

4. **Web服务器配置**
   - 配置Nginx/Apache
   - 配置HTTPS证书
   - 配置静态资源缓存

5. **监控与维护**
   - 日志收集
   - 性能监控
   - 定期备份

### 6.6 安全措施
- 密码加密存储
- CSRF防护
- XSS防护
- SQL注入防护
- 敏感数据加密
- 定期安全更新

## 7. 项目计划

### 7.1 开发阶段
1. **需求分析与设计（2周）**
   - 需求调研与确认
   - 系统架构设计
   - 数据库设计
   - 界面原型设计

2. **数据库设计（1周）**
   - 详细数据库表结构设计
   - 数据关系设计
   - 索引优化
   - 测试数据准备

3. **核心功能开发（4周）**
   - 用户认证与权限管理（5天）
   - 模板管理系统（5天）
   - 话题管理系统（3天）
   - 客户管理系统（3天）
   - 文案生成系统（5天）
   - 审核流程系统（5天）
   - 通知系统（2天）
   - API接口开发（2天）

4. **界面开发（2周）**
   - 管理后台界面（7天）
   - 客户界面（5天）
   - 响应式适配（2天）

5. **测试与修复（2周）**
   - 单元测试（3天）
   - 集成测试（4天）
   - 用户界面测试（3天）
   - Bug修复（4天）

6. **部署与上线（1周）**
   - 服务器环境准备（2天）
   - 应用部署（2天）
   - 上线前测试（2天）
   - 正式上线（1天）

### 7.2 交付物
1. **源代码**
   - 完整项目源代码
   - 代码注释文档
   - 版本控制仓库

2. **数据库设计文档**
   - 数据库表结构说明
   - 数据关系图
   - 索引设计说明
   - 初始化SQL脚本

3. **用户手册**
   - 管理员手册
   - 客户使用手册
   - 常见问题解答

4. **部署文档**
   - 环境要求说明
   - 安装步骤详解
   - 配置文件说明
   - 常见问题排查

5. **测试报告**
   - 测试用例
   - 测试结果
   - 性能测试报告
   - 安全测试报告

### 7.3 项目里程碑
1. **需求确认**：项目启动后2周
2. **数据库设计完成**：项目启动后3周
3. **核心功能开发完成**：项目启动后7周
4. **界面开发完成**：项目启动后9周
5. **测试完成**：项目启动后11周
6. **系统上线**：项目启动后12周

### 7.4 风险管理
1. **需求变更风险**
   - 风险描述：项目过程中需求可能发生变更
   - 应对策略：采用敏捷开发方法，定期确认需求，控制变更范围

2. **技术风险**
   - 风险描述：部分技术实现可能遇到困难
   - 应对策略：提前进行技术验证，准备备选方案

3. **进度风险**
   - 风险描述：开发进度可能滞后
   - 应对策略：合理规划任务，设置缓冲时间，定期检查进度

4. **质量风险**
   - 风险描述：系统质量不达标
   - 应对策略：建立完善的测试流程，进行代码审查

## 8. 系统总结

### 8.1 系统价值
小红书文案生成系统通过模板和关键词替换的方式，实现了文案的批量生成、审核和发布，具有以下价值：

1. **提高效率**：大幅减少文案创作时间，提高内容生产效率
2. **保证质量**：通过多级审核流程确保文案质量
3. **增强客户参与**：客户可直接参与文案审核和编辑
4. **灵活管理**：支持多种角色和权限，适应不同管理需求
5. **数据积累**：沉淀文案模板和话题资源，形成内容资产
6. **任务分批管理**：支持为客户创建多个任务并分批次生成文案，便于长期合作管理
7. **发布优先级控制**：通过优先级设置控制文案发布顺序，满足不同紧急程度需求

### 8.2 系统特点
1. **模板化生成**：通过标记系统实现内容灵活替换
2. **多级审核**：支持内部审核和客户审核双重保障
3. **定时展示**：按照设定的时间计划展示文案
4. **权限控制**：精细的权限管理满足不同角色需求
5. **通知机制**：实时通知确保工作流畅通
6. **历史记录**：完整记录文案变更历史
7. **API接口**：支持与其他系统集成
8. **任务批次管理**：支持为同一客户创建多个任务，每个任务可分多批次生成文案
9. **拒绝理由跟踪**：以聊天形式展示所有拒绝历史和理由，便于追踪问题
10. **发布管理**：支持设置发布优先级，控制文案发布顺序

### 8.3 系统流程总结
1. **模板准备阶段**：
   - 创建模板分类
   - 添加模板并定义标记
   - 设置话题库

2. **客户与任务管理阶段**：
   - 添加客户信息
   - 为客户创建任务（如5月任务、6月任务）
   - 设置每日展示数量和时间

3. **文案生成阶段**：
   - 选择客户和任务
   - 选择模板分类
   - 设置关键词
   - 批量生成文案
   - 可分多批次生成，满足不同需求

4. **审核流程阶段**：
   - 初审（可在系统设置中关闭）
   - 图片上传
   - 最终审核（可在系统设置中关闭）
   - 客户审核（通过分享链接访问）
   - 客户可拒绝并提供理由，文案回到初审状态

5. **发布管理阶段**：
   - 设置文案发布优先级
   - 通过API提供文案数据
   - 追踪发布状态

### 8.4 后续优化方向
1. **AI辅助**：引入AI技术辅助文案生成和优化
2. **数据分析**：增加数据分析功能，评估文案效果
3. **多平台发布**：直接对接小红书等平台API
4. **移动端应用**：开发配套移动应用
5. **内容库扩展**：扩展模板库和话题库
6. **自动化程度提升**：增加更多自动化流程
7. **批量操作增强**：增加更多批量处理功能，提高工作效率
8. **客户端功能扩展**：增强客户端操作体验和功能

### 8.5 技术选型总结
本系统采用Python + Flask + Flask-Admin + MySQL技术栈开发，具有以下优势：

1. **开发效率高**：Flask和Flask-Admin提供现成组件
2. **学习成本低**：Python语言易于学习和维护
3. **性能满足需求**：对于中小型应用性能足够
4. **扩展性好**：Flask框架扩展丰富
5. **部署简单**：支持多种部署方式，维护成本低
6. **适合快速迭代**：便于根据实际使用情况快速调整功能 

## 9. 系统设计评估

### 9.1 系统安全性评估

#### 9.1.1 访问控制
- **认证机制**：采用安全的用户认证机制
- **授权管理**：基于角色的精细权限控制
- **会话管理**：安全的会话处理机制
- **密码策略**：强密码策略和定期更换机制

#### 9.1.2 数据安全
- **敏感数据加密**：密码等敏感信息加密存储
- **传输加密**：使用HTTPS保护数据传输
- **备份策略**：定期数据备份和恢复测试
- **审计日志**：记录关键操作，便于安全审计

### 9.2 系统扩展性评估

#### 9.2.1 功能扩展性
- **模块化设计**：系统按功能模块划分，便于单独升级或替换模块
- **API接口预留**：关键功能点提供API接口，便于未来集成其他系统
- **配置驱动**：核心功能通过配置驱动，减少硬编码
- **插件机制**：预留插件扩展点，支持未来功能扩展

#### 9.2.2 用户界面扩展性
- **组件化设计**：界面元素组件化，便于重用和定制
- **主题支持**：支持界面主题切换
- **响应式设计**：适应不同设备和屏幕尺寸
- **自定义仪表盘**：允许用户自定义工作台布局 
